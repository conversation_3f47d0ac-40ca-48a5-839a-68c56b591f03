* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #ffffff 0%, #f0f8ff 50%, #e6f3ff 100%);
  overflow-x: hidden;
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  padding-top: 90px;
  padding-bottom: 150px;
}

.bg-particles {
  position: fixed;
  top: 0; left: 0;
  width: 100%; height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  background: rgba(0, 123, 255, 0.622);
  border-radius: 50%;
  animation: float 20s infinite linear;
}

.particle:nth-child(1) { width: 10px; height: 10px; left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { width: 15px; height: 15px; left: 20%; animation-delay: 2s; }
.particle:nth-child(3) { width: 8px; height: 8px; left: 30%; animation-delay: 4s; }
.particle:nth-child(4) { width: 12px; height: 12px; left: 40%; animation-delay: 6s; }
.particle:nth-child(5) { width: 20px; height: 20px; left: 50%; animation-delay: 0s; }
.particle:nth-child(6) { width: 10px; height: 10px; left: 10%; animation-delay: 0s; }
.particle:nth-child(7) { width: 15px; height: 15px; left: 20%; animation-delay: 2s; }
.particle:nth-child(8) { width: 8px; height: 8px; left: 30%; animation-delay: 4s; }
.particle:nth-child(9) { width: 12px; height: 12px; left: 40%; animation-delay: 6s; }
.particle:nth-child(10) { width: 20px; height: 20px; left: 50%; animation-delay: 0s; }
.particle:nth-child(11) { width: 10px; height: 10px; left: 10%; animation-delay: 0s; }
.particle:nth-child(12) { width: 15px; height: 15px; left: 20%; animation-delay: 2s; }
.particle:nth-child(13) { width: 8px; height: 8px; left: 30%; animation-delay: 4s; }
.particle:nth-child(14) { width: 12px; height: 12px; left: 40%; animation-delay: 6s; }
.particle:nth-child(15) { width: 20px; height: 20px; left: 50%; animation-delay: 0s; }

header {
  background: linear-gradient(90deg, #007bff, #00c6ff);
  color: white;
  padding: 1.5rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 999;
  animation: slideDown 1s ease-out;
}

header::before {
  content: '';
  position: absolute;
  top: 0; left: -100%;
  width: 100%; height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 3s infinite;
}

header h1 {
  font-size: 1.8rem;
  animation: glow 2s ease-in-out infinite alternate;
}

.nav-menu {
  display: flex;
  gap: 1.5rem;
}

.nav-menu a {
  color: white;
  text-decoration: none;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-menu a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: white;
  transition: left 0.3s ease;
  z-index: -1;
}

.nav-menu a:hover::before {
  left: 0;
}

.nav-menu a:hover {
  color: #000000;
  transform: translateY(-2px);
}

.blog-container {
  max-width: 1200px;
  margin: 40px auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  padding: 20px;
  flex: 1 0 auto;
}

.blog-card {
  background: linear-gradient(135deg, #eaf4ff 0%, #ddeeff 100%);
  padding: 30px 20px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  animation: cardFloat 1s ease-out;
  margin-bottom: 10px;
  border: 2px solid rgba(49, 83, 255, 0.536);
}

.blog-card::before {
  content: '';
  position: absolute;
  top: 0; left: 0;
  right: 0; bottom: 0;
  background: linear-gradient(45deg, rgba(0, 123, 255, 0.1), rgba(0, 198, 255, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.blog-card:hover::before {
  opacity: 1;
}

.blog-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 40px rgba(0, 123, 255, 0.2);
  border-color: #007bff;
}

.blog-card h2 {
  color: #0047b3;
  margin-bottom: 1rem;
  font-size: 1.3rem;
  animation: slideInLeft 1s ease-out;
  transition: color 0.3s ease;
}

.blog-card:hover h2 {
  color: #000000;
}

.blog-card p {
  animation: slideInRight 1s ease-out 0.2s both;
  line-height: 1.6;
  color: #333333;
  transition: color 0.3s ease;
}

.blog-card:hover p {
  color: #000000;
}

.blog-card .date {
  margin-top: 1rem;
  color: #666666;
  font-size: 0.9rem;
  animation: fadeInUp 1s ease-out 0.4s both;
}

footer {
  background: linear-gradient(90deg, #007bff, #00c6ff);
  color: white;
  text-align: center;
  padding: 0.5rem 2rem;
  font-size: 0.95rem;
  animation: slideUp 1s ease-out;
  position: fixed;
  bottom: 0;
  width: 100%;
  overflow: hidden;
  flex-shrink: 0;
  z-index: 999;
}

footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 0.2%;
  animation: gradientShift 3s ease-in-out infinite;
}

footer p {
  margin: 0;
  line-height: 1.5;
}

/* Animations */
@keyframes float {
  0% { transform: translateY(100vh) rotate(0deg); }
  100% { transform: translateY(-100px) rotate(360deg); }
}
@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}
@keyframes glow {
  0% { text-shadow: 0 0 5px rgba(255, 255, 255, 0.5); }
  100% { text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6); }
}
@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}
@keyframes fadeInUp {
  from { transform: translateY(30px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
@keyframes cardFloat {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
@keyframes slideInLeft {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
@keyframes slideInRight {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}
@keyframes slideUp {
  from { transform: translateY(50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}
@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

/* Responsive */
@media (max-width: 768px) {
  header {
    flex-direction: column;
    text-align: center;
    padding: 1rem 1.5rem;
  }
  .nav-menu {
    flex-wrap: wrap;
    justify-content: center;
    margin-top: 1rem;
    gap: 1rem;
  }
  .blog-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  footer {
    padding: 1.5rem 1rem;
    font-size: 0.85rem;
  }
}
@media (max-width: 480px) {
  header h1 {
    font-size: 1.5rem;
  }
  .nav-menu a {
    font-size: 0.9rem;
    padding: 6px 12px;
  }
  .blog-card {
    padding: 20px 15px;
  }
  .blog-card h2 {
    font-size: 1.1rem;
  }
  .blog-card p {
    font-size: 0.95rem;
  }
  footer {
    font-size: 0.8rem;
    padding: 1.5rem 1rem;
  }
}

html {
  scrollbar-width: none;
  -ms-overflow-style: none;
}
html::-webkit-scrollbar {
  display: none;
}
.modern-footer {
  background: linear-gradient(90deg, #007bff, #00c6ff);
  color: white;
  padding: 1rem 2rem;
  font-size: 0.9rem;
  border-top: 2px solid rgba(255, 255, 255, 0.2);
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  flex-wrap: wrap;
  gap: 1rem;
}

.footer-content p {
  margin: 0;
  white-space: nowrap;
}

.footer-links {
  display: flex;
  gap: 1.5rem;
  flex-wrap: wrap;
}

.footer-links a {
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.4rem;
  transition: color 0.3s ease;
  white-space: nowrap;
}

.footer-links a:hover {
  color: #ffed4e;
}
